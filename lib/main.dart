import 'package:flutter/material.dart';
import 'dart:async';
import 'screens/reports_screen.dart';
import 'database/database_helper.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'components/custom_form_fields.dart';
import 'theme/app_theme.dart';
import 'screens/splash_screen.dart';
import 'services/notification_service.dart';
import 'screens/patient_form.dart';


void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await NotificationService().init();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Dr. Diary',
      theme: AppTheme.lightTheme,
      home: const SplashScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Swarna Diary'),
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor,
              ),
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: Colors.white,
                    child: Icon(Icons.person, size: 40, color: Colors.pink),
                  ),
                  SizedBox(height: 10),
                  Text(
                    'Dr. Swarna',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                    ),
                  ),
                ],
              ),
            ),
            ListTile(
              leading: const Icon(Icons.notifications_active),
              title: const Text('Set Reminder Time'),
              onTap: () async {
                TimeOfDay? selectedTime = await showTimePicker(
                  context: context,
                  initialTime: const TimeOfDay(hour: 21, minute: 0),
                );
                if (selectedTime != null) {
                  await NotificationService().updateNotificationTime(
                    selectedTime.hour,
                    selectedTime.minute,
                  );
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(
                          'Reminder set for ${selectedTime.format(context)}',
                        ),
                      ),
                    );
                  }
                }
              },
            ),
            const Divider(),
            // Add more drawer items here if needed
          ],
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Expanded(
              child: GridView.count(
                crossAxisCount: 2,
                mainAxisSpacing: 16,
                crossAxisSpacing: 16,
                children: [
                  _buildMenuCard(
                    context,
                    'Data Entry',
                    Icons.edit_note_rounded,
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const DataEntryForm(),
                      ),
                    ),
                  ),
                  _buildMenuCard(
                    context,
                    'Reports',
                    Icons.bar_chart_rounded,
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const ReportsScreen(),
                      ),
                    ),
                  ),
                  _buildMenuCard(
                    context,
                    'Patients',
                    Icons.people_alt_rounded,
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const PatientForm(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuCard(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48,
              color: Theme.of(context).primaryColor,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DataEntryForm extends StatefulWidget {
  const DataEntryForm({super.key});

  @override
  State<DataEntryForm> createState() => _DataEntryFormState();
}

class _DataEntryFormState extends State<DataEntryForm> {
  // Add these near other field declarations
  String _selectedSurgeryType = 'NDV';
  final List<String> surgeryTypes = [
    'NDV',
    'PS',
    'TAT',
    'LSCS',
    'LSCS(ST)',
    'TAH',
    'VH',
    'LAP',
  ];
  final _db = DatabaseHelper();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _surgeryTypeController = TextEditingController();
  final TextEditingController _amountController = TextEditingController();
  final TextEditingController _remarksController = TextEditingController();
  String _selectedHospital = 'Mettupalayam GH';
  String _hospitalType = 'Government';
  final List<String> governmentHospitals = [
    'Mettupalayam GH',
    'SS Kulam',
    'Pogalur',
  ];
  
  final List<String> privateHospitals = [
    'Sowmiya',
    'Sumathi',
    'Others',
  ];

  @override
  void initState() {
    super.initState();
    _loadEntries();
  }

  List<Map<String, dynamic>> _entries = [];

  Future<void> _loadEntries() async {
    final entries = await _db.getEntries();
    setState(() {
      _entries = entries.reversed.toList(); // Reverse the list to show newest entries first
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      setState(() {
        _dateController.text = "${picked.day}/${picked.month}/${picked.year}";
      });
    }
  }

  void _clearFields() {
    _dateController.clear();
    _amountController.clear();
    _remarksController.clear();
    setState(() {
      _selectedHospital = _hospitalType == 'Government' 
          ? governmentHospitals[0] 
          : privateHospitals[0];
      _selectedSurgeryType = surgeryTypes[0];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Daily Entry'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            CustomFormField(
              controller: _dateController,
              label: 'Date',
              readOnly: true,
              onTap: () => _selectDate(context),
              prefixIcon: const Icon(Icons.calendar_today),
            ),
            // Add this new widget after the date field
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Row(
                children: [
                  Expanded(
                    child: RadioListTile<String>(
                      title: const Text('Government'),
                      value: 'Government',
                      groupValue: _hospitalType,
                      onChanged: (String? value) {
                        setState(() {
                          _hospitalType = value!;
                          _selectedHospital = governmentHospitals[0];
                          if (value == 'Government') {
                            _amountController.clear();
                          }
                        });
                      },
                    ),
                  ),
                  Expanded(
                    child: RadioListTile<String>(
                      title: const Text('Private'),
                      value: 'Private',
                      groupValue: _hospitalType,
                      onChanged: (String? value) {
                        setState(() {
                          _hospitalType = value!;
                          _selectedHospital = privateHospitals[0];
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: DropdownButtonFormField<String>(
                value: _selectedSurgeryType,
                decoration: const InputDecoration(
                  labelText: 'Type of Surgery',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.medical_services),
                ),
                items: surgeryTypes.map((String type) {
                  return DropdownMenuItem<String>(
                    value: type,
                    child: Text(type),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedSurgeryType = newValue!;
                  });
                },
              ),
            ),
            
            if (_hospitalType == 'Private')
              CustomFormField(
                controller: _amountController,
                label: 'Amount',
                keyboardType: TextInputType.number,
                prefixIcon: const Icon(Icons.currency_rupee),
              ),
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: DropdownButtonFormField<String>(
                value: _selectedHospital,
                decoration: const InputDecoration(
                  labelText: 'Hospital',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.local_hospital),
                ),
                items: (_hospitalType == 'Government' ? governmentHospitals : privateHospitals)
                    .map((String hospital) {
                  return DropdownMenuItem<String>(
                    value: hospital,
                    child: Text(hospital),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedHospital = newValue!;
                  });
                },
              ),
            ),
            TextField(
              controller: _remarksController,
              decoration: const InputDecoration(
                labelText: 'Remarks',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
                prefixIcon: Icon(Icons.note),
              ),
              maxLines: 3,
              textAlignVertical: TextAlignVertical.top,
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                TextButton(
                  onPressed: _clearFields,
                  child: const Text('Clear'),
                ),
                ElevatedButton(
                  onPressed: _addEntry,
                  child: const Text('Save'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: _entries.length,
                itemBuilder: (context, index) {
                  return Card(
                    child: ListTile(
                      title: Text('${_entries[index]['surgery_type']} - ${_entries[index]['hospital']}'),
                      subtitle: Text(
                        'Date: ${_entries[index]['date']}\n'
                        'Amount: ₹${_entries[index]['amount']}\n'
                        'Remarks: ${_entries[index]['remarks']}',
                      ),
                      isThreeLine: true,
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          IconButton(
                            icon: const Icon(Icons.edit),
                            onPressed: () {
                              setState(() {
                                _dateController.text = _entries[index]['date'];
                                _selectedSurgeryType = _entries[index]['surgery_type'];
                                _amountController.text = _entries[index]['amount'].toString();
                                _remarksController.text = _entries[index]['remarks'];
                                _hospitalType = _entries[index]['hospital_type'];
                                _selectedHospital = _entries[index]['hospital'];
                              });
                            },
                          ),
                          IconButton(
                            icon: const Icon(Icons.delete, color: Colors.red),
                            onPressed: () async {
                              await _db.deleteEntry(_entries[index]['id']);
                              _loadEntries();
                            },
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _addEntry() async {
    await _db.insertEntry({
      'date': _dateController.text,
      'surgery_type': _selectedSurgeryType,  // Updated to use selected value
      'amount': double.tryParse(_amountController.text) ?? 0.0,
      'remarks': _remarksController.text,
      'hospital': _selectedHospital,
      'hospital_type': _hospitalType,
    });
    _clearFields();
    _loadEntries();
  }
}