import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static Database? _database;

  factory DatabaseHelper() => _instance;

  DatabaseHelper._internal();

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _getDatabase();
    return _database!;
  }

  Future<Database> _getDatabase() async {
    final dbPath = await getDatabasesPath();
    return openDatabase(
      join(dbPath, 'entries.db'),
      onCreate: (db, version) async {  // Add async here
        // Create entries table
        await db.execute(
          '''CREATE TABLE entries(
            id INTEGER PRIMARY KEY AUTOINCREMENT, 
            date TEXT,
            surgery_type TEXT,
            amount REAL,
            hospital TEXT,
            hospital_type TEXT,
            remarks TEXT
          )'''
        );
        
        // Create patients table
        await db.execute(
          '''CREATE TABLE patients(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT,
            name TEXT,
            problem TEXT,
            solution TEXT,
            remarks TEXT
          )'''
        );
      },
      version: 4,  // Increment version number
      onUpgrade: (db, oldVersion, newVersion) async {
        // Recreate both tables on upgrade
        await db.execute('DROP TABLE IF EXISTS entries');
        await db.execute('DROP TABLE IF EXISTS patients');
        
        await db.execute(
          '''CREATE TABLE entries(
            id INTEGER PRIMARY KEY AUTOINCREMENT, 
            date TEXT,
            surgery_type TEXT,
            amount REAL,
            hospital TEXT,
            hospital_type TEXT,
            remarks TEXT
          )'''
        );
        
        await db.execute(
          '''CREATE TABLE patients(
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            date TEXT,
            name TEXT,
            problem TEXT,
            solution TEXT,
            remarks TEXT
          )'''
        );
      },
    );
  }

  Future<List<Map<String, dynamic>>> getEntries() async {
    final db = await database;
    return await db.query('entries', orderBy: 'id ASC'); // Add DESC for descending order
  }

  Future<void> insertEntry(Map<String, dynamic> entry) async {
    final db = await database;
    await db.insert('entries', entry);
  }

  Future<void> deleteEntry(int id) async {
    final db = await database;
    await db.delete(
      'entries',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  Future<List<Map<String, dynamic>>> getEntriesByDateRange(String startDate, String endDate) async {
    final db = await database;
    return await db.query(
      'entries',
      where: 'date BETWEEN ? AND ?',
      whereArgs: [startDate, endDate],
      orderBy: 'date DESC',
    );
  }

  Future<List<Map<String, dynamic>>> getEntriesByMonth(int month, int year) async {
    final db = await database;
    return await db.query(
      'entries',
      where: "strftime('%m', date) = ? AND strftime('%Y', date) = ?",
      whereArgs: [month.toString().padLeft(2, '0'), year.toString()],
      orderBy: 'date DESC',
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute(
      '''CREATE TABLE entries(
        id INTEGER PRIMARY KEY AUTOINCREMENT, 
        date TEXT,
        surgery_type TEXT,
        amount REAL,
        hospital TEXT,
        hospital_type TEXT,
        remarks TEXT
      )'''
    );
    
    // Add patients table creation
    await db.execute('''
      CREATE TABLE patients(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT,
        name TEXT,
        problem TEXT,
        solution TEXT,
        remarks TEXT
      )
    ''');
  }

  // Add these methods for patient operations
  Future<int> insertPatient(Map<String, dynamic> patient) async {
    final db = await database;
    return await db.insert('patients', patient);
  }

  Future<List<Map<String, dynamic>>> getPatients() async {
    final db = await database;
    return await db.query('patients', orderBy: 'id DESC');
  }

  Future<int> deletePatient(int id) async {
    final db = await database;
    return await db.delete('patients', where: 'id = ?', whereArgs: [id]);
  }
}