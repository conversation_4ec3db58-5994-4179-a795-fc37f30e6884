import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../components/custom_form_fields.dart';  // Add this import

class ReportsScreen extends StatefulWidget {
  const ReportsScreen({super.key});

  @override
  State<ReportsScreen> createState() => _ReportsScreenState();
}

class _ReportsScreenState extends State<ReportsScreen> {
  final _db = DatabaseHelper();
  List<Map<String, dynamic>> _entries = [];
  final TextEditingController _startDateController = TextEditingController();
  final TextEditingController _endDateController = TextEditingController();
  String _selectedReportType = 'Date wise';
  final List<String> _reportTypes = [
    'Date wise',
    'Surgery wise',
    'Amount wise',
    'Hospital wise',
  ];

  @override
  void initState() {
    super.initState();
    _loadEntries();
  }

  Future<void> _loadEntries() async {
    final entries = await _db.getEntries();
    setState(() {
      _entries = entries;
    });
  }

  Future<void> _selectDate(BuildContext context, TextEditingController controller) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      setState(() {
        controller.text = "${picked.day}/${picked.month}/${picked.year}";
      });
    }
  }

  Future<void> _generateReport() async {
    if (_startDateController.text.isEmpty || _endDateController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select both start and end dates')),
      );
      return;
    }

    final entries = await _db.getEntriesByDateRange(
      _startDateController.text,
      _endDateController.text,
    );

    switch (_selectedReportType) {
      case 'Date wise':
        setState(() => _entries = entries);
        break;
        
      case 'Surgery wise':
        final surgeryCount = <String, int>{};
        for (var entry in entries) {
          final type = entry['surgery_type'] as String;
          surgeryCount[type] = (surgeryCount[type] ?? 0) + 1;
        }
        setState(() {
          _entries = entries;
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Surgery Summary (${_startDateController.text} - ${_endDateController.text})'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: surgeryCount.entries.map((e) => 
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4),
                      child: Text('${e.key}: ${e.value} cases'),
                    )
                  ).toList(),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        });
        break;

      case 'Hospital wise':
        final hospitalCount = <String, Map<String, int>>{
          'Government': {},
          'Private': {},
        };
        
        for (var entry in entries) {
          final type = entry['hospital_type'] as String;
          final hospital = entry['hospital'] as String;
          hospitalCount[type]![hospital] = (hospitalCount[type]![hospital] ?? 0) + 1;
        }
        
        setState(() {
          _entries = entries;
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Hospital Summary (${_startDateController.text} - ${_endDateController.text})'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Government Hospitals:', style: TextStyle(fontWeight: FontWeight.bold)),
                    ...hospitalCount['Government']!.entries.map((e) => 
                      Padding(
                        padding: const EdgeInsets.only(left: 16, top: 4),
                        child: Text('${e.key}: ${e.value} cases'),
                      )
                    ),
                    const SizedBox(height: 16),
                    const Text('Private Hospitals:', style: TextStyle(fontWeight: FontWeight.bold)),
                    ...hospitalCount['Private']!.entries.map((e) => 
                      Padding(
                        padding: const EdgeInsets.only(left: 16, top: 4),
                        child: Text('${e.key}: ${e.value} cases'),
                      )
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        });
        break;

      case 'Amount wise':
        double totalAmount = 0;
        Map<String, double> hospitalAmounts = {};
        
        for (var entry in entries) {
          final amount = entry['amount'] as num;
          final hospital = entry['hospital'] as String;
          totalAmount += amount;
          hospitalAmounts[hospital] = (hospitalAmounts[hospital] ?? 0) + amount;
        }
        
        setState(() {
          _entries = List<Map<String, dynamic>>.from(entries)
            ..sort((a, b) => (b['amount'] as num).compareTo(a['amount'] as num));
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Amount Summary (${_startDateController.text} - ${_endDateController.text})'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Total Amount: ₹${totalAmount.toStringAsFixed(2)}',
                        style: const TextStyle(fontWeight: FontWeight.bold)),
                    const SizedBox(height: 16),
                    const Text('Hospital-wise Breakdown:'),
                    ...hospitalAmounts.entries.map((e) => 
                      Padding(
                        padding: const EdgeInsets.only(left: 16, top: 4),
                        child: Text('${e.key}: ₹${e.value.toStringAsFixed(2)}'),
                      )
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        });
        break;

      case 'Hospital wise':
        final hospitalSurgeryCount = <String, Map<String, Map<String, int>>>{
          'Government': {},
          'Private': {},
        };
        
        for (var entry in entries) {
          final type = entry['hospital_type'] as String;
          final hospital = entry['hospital'] as String;
          final surgery = entry['surgery_type'] as String;
          
          hospitalSurgeryCount[type]![hospital] ??= {};
          hospitalSurgeryCount[type]![hospital]![surgery] = 
              (hospitalSurgeryCount[type]![hospital]![surgery] ?? 0) + 1;
        }
        
        setState(() {
          _entries = entries;
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Hospital Summary (${_startDateController.text} - ${_endDateController.text})'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('Government Hospitals:', style: TextStyle(fontWeight: FontWeight.bold)),
                    ...hospitalSurgeryCount['Government']!.entries.map((hospital) => 
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 8, top: 16),
                            child: Text(hospital.key, 
                              style: const TextStyle(fontWeight: FontWeight.w500)),
                          ),
                          ...hospital.value.entries.map((surgery) =>
                            Padding(
                              padding: const EdgeInsets.only(left: 24, top: 4),
                              child: Text('${surgery.key}: ${surgery.value} cases'),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    const Text('Private Hospitals:', style: TextStyle(fontWeight: FontWeight.bold)),
                    ...hospitalSurgeryCount['Private']!.entries.map((hospital) => 
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 8, top: 16),
                            child: Text(hospital.key,
                              style: const TextStyle(fontWeight: FontWeight.w500)),
                          ),
                          ...hospital.value.entries.map((surgery) =>
                            Padding(
                              padding: const EdgeInsets.only(left: 24, top: 4),
                              child: Text('${surgery.key}: ${surgery.value} cases'),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        });
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reports'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            DropdownButtonFormField<String>(
              value: _selectedReportType,
              decoration: const InputDecoration(
                labelText: 'Report Type',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.summarize),
              ),
              items: _reportTypes.map((String type) {
                return DropdownMenuItem<String>(
                  value: type,
                  child: Text(type),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedReportType = newValue!;
                });
              },
            ),
            const SizedBox(height: 16),
            if (_selectedReportType == 'Date wise')
              Row(
                children: [
                  Expanded(
                    child: CustomFormField(
                      controller: _startDateController,
                      label: 'Start Date',
                      readOnly: true,
                      onTap: () => _selectDate(context, _startDateController),
                      prefixIcon: const Icon(Icons.calendar_today),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: CustomFormField(
                      controller: _endDateController,
                      label: 'End Date',
                      readOnly: true,
                      onTap: () => _selectDate(context, _endDateController),
                      prefixIcon: const Icon(Icons.calendar_today),
                    ),
                  ),
                ],
              ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _generateReport,
              icon: const Icon(Icons.assessment),
              label: const Text('Generate Report'),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: _entries.length,
                itemBuilder: (context, index) {
                  return Card(
                    elevation: 4,
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      title: Text(
                        '${_entries[index]['surgery_type']} - ${_entries[index]['hospital']}',
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Date: ${_entries[index]['date']}'),
                          Text('Amount: ₹${_entries[index]['amount']}'),
                          if (_entries[index]['remarks']?.isNotEmpty ?? false)
                            Text('Remarks: ${_entries[index]['remarks']}'),
                        ],
                      ),
                      isThreeLine: true,
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}