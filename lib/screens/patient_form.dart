import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../components/custom_form_fields.dart';

class PatientForm extends StatefulWidget {
  const PatientForm({super.key});

  @override
  State<PatientForm> createState() => _PatientFormState();
}

class _PatientFormState extends State<PatientForm> {
  final _db = DatabaseHelper();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _problemController = TextEditingController();
  final TextEditingController _solutionController = TextEditingController();
  final TextEditingController _remarksController = TextEditingController();
  List<Map<String, dynamic>> _patients = [];

  @override
  void initState() {
    super.initState();
    _loadPatients();
  }

  Future<void> _loadPatients() async {
    final patients = await _db.getPatients();
    setState(() {
      _patients = patients;
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      setState(() {
        _dateController.text = "${picked.day}/${picked.month}/${picked.year}";
      });
    }
  }

  void _clearFields() {
    _dateController.clear();
    _nameController.clear();
    _problemController.clear();
    _solutionController.clear();
    _remarksController.clear();
  }

  Future<void> _addPatient() async {
    await _db.insertPatient({
      'date': _dateController.text,
      'name': _nameController.text,
      'problem': _problemController.text,
      'solution': _solutionController.text,
      'remarks': _remarksController.text,
    });
    _clearFields();
    _loadPatients();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Patient Records'),
      ),
      resizeToAvoidBottomInset: true,  // Add this line
      body: Column(
        children: [
          Expanded(
            flex: 2,
            child: SingleChildScrollView(
              padding: EdgeInsets.only(
                top: 16.0,
                left: 16.0,
                right: 16.0,
                bottom: MediaQuery.of(context).viewInsets.bottom + 16.0,
              ),
              child: Column(
                children: [
                  CustomFormField(
                    controller: _dateController,
                    label: 'Date',
                    readOnly: true,
                    onTap: () => _selectDate(context),
                    prefixIcon: const Icon(Icons.calendar_today),
                  ),
                  const SizedBox(height: 16),
                  CustomFormField(
                    controller: _nameController,
                    label: 'Patient Name',
                    prefixIcon: const Icon(Icons.person),
                  ),
                  const SizedBox(height: 16),
                  CustomFormField(
                    controller: _problemController,
                    label: 'Problem',
                    maxLines: 3,
                    prefixIcon: const Icon(Icons.medical_services),
                  ),
                  const SizedBox(height: 16),
                  CustomFormField(
                    controller: _solutionController,
                    label: 'Solution',
                    maxLines: 3,
                    prefixIcon: const Icon(Icons.healing),
                  ),
                  const SizedBox(height: 16),
                  CustomFormField(
                    controller: _remarksController,
                    label: 'Remarks',
                    maxLines: 3,
                    prefixIcon: const Icon(Icons.note),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      TextButton(
                        onPressed: _clearFields,
                        child: const Text('Clear'),
                      ),
                      ElevatedButton(
                        onPressed: _addPatient,
                        child: const Text('Save'),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const Divider(),
          Expanded(
            flex: 1,
            child: ListView.builder(
              itemCount: _patients.length,
              itemBuilder: (context, index) {
                return Card(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ListTile(
                    title: Text(_patients[index]['name']),
                    subtitle: Text(
                      'Date: ${_patients[index]['date']}\n'
                      'Problem: ${_patients[index]['problem']}\n'
                      'Solution: ${_patients[index]['solution']}\n'
                      'Remarks: ${_patients[index]['remarks']}',
                    ),
                    isThreeLine: true,
                    trailing: IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () async {
                        await _db.deletePatient(_patients[index]['id']);
                        _loadPatients();
                      },
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}