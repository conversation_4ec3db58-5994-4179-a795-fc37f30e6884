import 'package:flutter/material.dart';

class CustomFormField extends StatelessWidget {
  final TextEditingController controller;
  final String label;
  final bool readOnly;
  final VoidCallback? onTap;
  final Icon? prefixIcon;
  final TextInputType? keyboardType;
  final int? maxLines;  // Add this parameter

  const CustomFormField({
    super.key,
    required this.controller,
    required this.label,
    this.readOnly = false,
    this.onTap,
    this.prefixIcon,
    this.keyboardType,
    this.maxLines = 1,  // Add default value
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(  // Add SizedBox wrapper
      width: double.infinity,  // Take full width
      child: TextField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
          prefixIcon: prefixIcon,
          contentPadding: const EdgeInsets.symmetric(  // Add proper padding
            vertical: 16.0,
            horizontal: 12.0,
          ),
        ),
        readOnly: readOnly,
        onTap: onTap,
        keyboardType: keyboardType,
        maxLines: maxLines,
      ),
    );
  }
}

class CustomDropdownField extends StatelessWidget {
  final String value;
  final List<String> items;
  final String label;
  final void Function(String?) onChanged;

  const CustomDropdownField({
    super.key,
    required this.value,
    required this.items,
    required this.label,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: DropdownButtonFormField<String>(
        value: value,
        decoration: InputDecoration(
          labelText: label,
          border: const OutlineInputBorder(),
        ),
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: Text(item),
          );
        }).toList(),
        onChanged: onChanged,
      ),
    );
  }
}

class CustomRadioField extends StatelessWidget {
  final String groupValue;
  final Function(String?) onChanged;
  final String label;

  const CustomRadioField({
    super.key,
    required this.groupValue,
    required this.onChanged,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: const TextStyle(fontSize: 16)),
          Row(
            children: [
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('Government'),
                  value: 'Government',
                  groupValue: groupValue,
                  onChanged: onChanged,
                ),
              ),
              Expanded(
                child: RadioListTile<String>(
                  title: const Text('Private'),
                  value: 'Private',
                  groupValue: groupValue,
                  onChanged: onChanged,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}