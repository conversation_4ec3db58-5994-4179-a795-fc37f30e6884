{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985dc99f7e75fd35de85637998098c7d8d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e19228be12252edf38dd6ca51b6df219", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a7b932d202db7b15197b1644237dbc8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98641391e496a4a37f3ef5b39b0f291421", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a7b932d202db7b15197b1644237dbc8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite/sqflite-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite/sqflite-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite/sqflite.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite", "PRODUCT_NAME": "sqflite", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cab47b6636e5c33377ce749e179bf40d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984c6f2c37be18cfb3a62007a440a0baef", "guid": "bfdfe7dc352907fc980b868725387e98a3aecb2067b7ea581e8574efa04b567d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a14ad87131564d5bcd514e6d7e9e150", "guid": "bfdfe7dc352907fc980b868725387e98b7128cf62df1f72cd0cdbd10dbfb5a48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803a726ae4f2929f98838b886d51ffe05", "guid": "bfdfe7dc352907fc980b868725387e988da4d12a0839b91093a085f615c1fa75", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9387bef839c4063484c0df44d710f05", "guid": "bfdfe7dc352907fc980b868725387e985a84bd6c8b851be0d5c6d51397ae8828", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd336c9a46d7de7c77c79bbc534d0052", "guid": "bfdfe7dc352907fc980b868725387e98b4a5d7d8669929efe0c11e0806f1859c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc31f002e38a4dbda4b8d9a15bdc9350", "guid": "bfdfe7dc352907fc980b868725387e98cb447f826ba1a33b2d0dd7910b36513c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c683a1de776da42497f8c78d946c6fa0", "guid": "bfdfe7dc352907fc980b868725387e981fed629fd56e805e0638afc8ba9d3ac9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c05fa6eda885c2a85263a6a31a5f670c", "guid": "bfdfe7dc352907fc980b868725387e9890fd91645ccd00b950ef24b280f15176", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eba2070a40751144ae7f603dfded2b5b", "guid": "bfdfe7dc352907fc980b868725387e98e2d1d88ae6d3c08f75c4ffd0dd9ae1bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98475fc1c4f41ee63b38ec6d79c72937a3", "guid": "bfdfe7dc352907fc980b868725387e987ff52561c7c3987c0c30aa5f3ce31535", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984858ec465d9daca6cd7d7578aa056bb0", "guid": "bfdfe7dc352907fc980b868725387e98d8036c2b834c31343f46d5eadca6705f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9c799fe2905ab4a20d2319cd3231ac1", "guid": "bfdfe7dc352907fc980b868725387e98626756356e2657df1e0c4e0a7b81b16b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98ad2dc82ab8ed911bd330f7f2eae217f7", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c4d63859bd534e83b2e4c79f110d1b6b", "guid": "bfdfe7dc352907fc980b868725387e98f725d4e8e04b01e297b4f883c6405f63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983176b32236a83658c39b5cbc040ec23a", "guid": "bfdfe7dc352907fc980b868725387e985b9dd9f198dd7f5fe4e110fa69849404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab3deb27c8d83b50f93b1f8e9970240", "guid": "bfdfe7dc352907fc980b868725387e98f0745003017e70c063aa84350dd0ce5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb1daa40d63857343ff2d5e359c0983b", "guid": "bfdfe7dc352907fc980b868725387e9810865cb1b4f02eee6a49f7f22e7b32d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825d73cd60f48dd40774046522fbf8dbf", "guid": "bfdfe7dc352907fc980b868725387e986e78ef2bdb945f9609518b168e3d8412"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804a7cdb255a82e2a80eb5896b02019dc", "guid": "bfdfe7dc352907fc980b868725387e985eee8afba99af04aaac12c9debff909c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c44359762aebe145d3b2dea0fcf891d", "guid": "bfdfe7dc352907fc980b868725387e98d0697efc507a4d256632aae35755e8f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b85841a7b88e548763385e4f01188d3b", "guid": "bfdfe7dc352907fc980b868725387e9867925368c2a5c61b7f6878c61ccc7060"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a0104e40cb45436683e563c32fdaa06", "guid": "bfdfe7dc352907fc980b868725387e98804cc761cfff31e460135a56a0f0e991"}], "guid": "bfdfe7dc352907fc980b868725387e98da8caa0468a6494c539bcc9a3f56496f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e980d562db8451c55dccc31ac9670df4bb5"}], "guid": "bfdfe7dc352907fc980b868725387e989c121c0173be5443faa5b2120d803293", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c44defa1c51e6619c9bc20d8804a8b84", "targetReference": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321"}], "guid": "bfdfe7dc352907fc980b868725387e984c8ab30283e75361b6a65cb914f55e53", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9894b6f514aa32ee4cfdd7fc11c1ff5321", "name": "sqflite-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e983786431ce548989b846bbf1a7384f58e", "name": "sqflite", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9892137925c03f59a4fb600ced1a959f92", "name": "sqflite.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}